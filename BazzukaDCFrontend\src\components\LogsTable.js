import React, { useEffect, useRef, useState } from "react";
import {
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CAvatar,
  CProgress,
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CCard,
  CCardHeader,
  CCardBody,
  CCol,
  CRow,
} from "@coreui/react";
import { cilPeople, cilOptions } from "@coreui/icons";
import { CIcon } from "@coreui/icons-react";
import InboundCallIcon from "../assets/icons/incoming-call.png";
import OutboundCallIcon from "../assets/icons/outgoing-call.png";
import InboundEmailIcon from "../assets/icons/incoming-email.png";
import OutboundEmailIcon from "../assets/icons/outgoing-email.png";
import InboundTextIcon from "../assets/icons/incoming-message.png";
import OutboundTextIcon from "../assets/icons/outgoing-message.png";
import UserIcon from "../assets/icons/user.png";
import CommLogCard from "./CommLogCard";
import { useFormattedTimestamp } from "../utils/dateUtils";
import { formatTranscript } from "../utils/textUtils";
import TranscriptView from "./TranscriptView";
import { getApiUrl } from '../utils/apiConfig'

const communicationIcons = {
  "inbound call": InboundCallIcon,
  "outbound call": OutboundCallIcon,
  "inbound email": InboundEmailIcon,
  "outbound email": OutboundEmailIcon,
  "inbound text": InboundTextIcon,
  "outbound text": OutboundTextIcon,
};

const getPaymentLikelihoodColor = (value, direction) => {
  if (direction === "outbound") return "secondary"; // Gray for outbound
  if (value >= 4) return "success"; // Green
  if (value >= 2) return "warning"; // Orange
  return "danger"; // Red
};

const getPaymentLikelihoodText = (value, direction) => {
  if (direction === "outbound") return "Outbound";
  return `${(value * 100) / 5}%`;
};

const LogsTable = () => {
  const [tableExample, setTableExample] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCommLog, setSelectedCommLog] = useState(null);
  const [visible, setVisible] = useState(false);
  const observerRef = useRef(null);
  const formatTimestamp = useFormattedTimestamp();

  const limit = 10; // Number of items to fetch per request
  const [offset, setOffset] = useState(0); // Current offset for pagination

  const fetchData = async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);

    try {
      const response = await fetch(
        getApiUrl(`/v0/communications?limit=${limit}&offset=${offset}`),
        {
          method: "GET",
          headers: {
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
      const json_data = await response.json();
      const data = json_data.data;

      console.log('lt', data);

      // Format the data to include formatted timestamp with user's timezone
      const formattedData = data.map(item => {
        const timestamp = new Date(item.timestamp);
        const formattedTimestamp = formatTimestamp(timestamp);

        // Split the formatted timestamp into date and time parts for backward compatibility
        const [datePart, timePart] = formattedTimestamp.split(', ');

        return {
          ...item,
          date: datePart,
          time: timePart,
          formattedTimestamp: formattedTimestamp
        };
      });

      if (formattedData.length < limit) {
        setHasMore(false); // No more data to fetch
      }

      setTableExample((prev) => [...prev, ...formattedData]);
      setOffset((prev) => prev + limit); // Increment offset
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          fetchData();
        }
      },
      { threshold: 1 }
    );

    if (observerRef.current) observer.observe(observerRef.current);
    return () => observer.disconnect();
  }, [hasMore, offset]); // Depend on `offset` to update correctly

  const filteredData = tableExample.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleViewMore = (commLog) => {
    setSelectedCommLog(commLog);
    setVisible(true);

    // Fetch summary and transcript data
    fetch(getApiUrl(`/v0/communications/${commLog.comm_id}`), {
      method: "GET",
      headers: {
        "ngrok-skip-browser-warning": "true",
        "Access-Control-Allow-Origin": "*",
      },
    })
      .then(response => response.json())
      .then(data => {
        setSelectedCommLog(prev => ({
          ...prev,
          summary: data.summary,
          transcript: data.transcript
        }));
      })
      .catch(error => {
        console.error("Error fetching communication details:", error);
      });
  };

  const handleUpdate = (updatedCommLog) => {
    setTableExample(prev =>
      prev.map(item =>
        item.id === updatedCommLog.id ? updatedCommLog : item
      )
    );
  };

  return (
    <>
      <div style={{ height: "500px", overflowY: "auto" }}>
        <input
          type="text"
          placeholder="Search by name"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="mb-3 form-control"
        />
        <CTable align="middle" className="mb-0 mt-1 border" hover responsive>
          <CTableHead className="text-nowrap">
            <CTableRow>
              <CTableHeaderCell className="bg-body-tertiary text-center">
                <CIcon icon={cilPeople} />
              </CTableHeaderCell>
              <CTableHeaderCell className="bg-body-tertiary">User</CTableHeaderCell>
              <CTableHeaderCell className="bg-body-tertiary text-center">
                Channel
              </CTableHeaderCell>
              <CTableHeaderCell className="bg-body-tertiary">
                Payment Likelihood
              </CTableHeaderCell>
              <CTableHeaderCell className="bg-body-tertiary">
                Timestamp
              </CTableHeaderCell>
              <CTableHeaderCell className="bg-body-tertiary text-center">
                Actions
              </CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {filteredData.map((item, index) => (
              <CTableRow key={index}>
                <CTableDataCell className="text-center">
                  <CAvatar size="md" src={UserIcon} status={
                    item.payment_likelihood <= 2
                      ? "danger"
                      : item.payment_likelihood === 3
                      ? "warning"
                      : "success"
                  } />
                </CTableDataCell>
                <CTableDataCell>
                  <div>{item.name}</div>
                  <div className="small text-body-secondary text-nowrap"></div>
                </CTableDataCell>
                <CTableDataCell className="text-center">
                  <div className="d-flex align-items-center">
                    <img
                      src={communicationIcons[
                        (item.direction + " " + item.channel).toLowerCase()
                      ]}
                      alt={item.channel}
                      className="me-2"
                      style={{ width: "24px", height: "24px" }}
                    />
                  </div>
                </CTableDataCell>
                <CTableDataCell>
                  {item.direction === "outbound" && item.channel !== "call" ? (
                    <div className="text-muted">N/A</div>
                  ) : (
                    <div className="d-flex align-items-center gap-2">
                      <div className="fw-semibold" style={{ minWidth: "45px" }}>
                        {(item.payment_likelihood * 100) / 5}%
                      </div>
                      <div className="flex-grow-1">
                        <CProgress
                          thin
                          color={
                            item.payment_likelihood <= 2
                              ? "danger"
                              : item.payment_likelihood === 3
                              ? "warning"
                              : "success"
                          }
                          value={(item.payment_likelihood * 100) / 5}
                        />
                      </div>
                    </div>
                  )}
                </CTableDataCell>
                <CTableDataCell>
                  {item.formattedTimestamp}
                </CTableDataCell>
                <CTableDataCell className="text-center">
                  <CButton
                    color="primary"
                    variant="ghost"
                    onClick={() => handleViewMore(item)}
                  >
                    <CIcon icon={cilOptions} />
                  </CButton>
                </CTableDataCell>
              </CTableRow>
            ))}
          </CTableBody>
        </CTable>
        <div ref={observerRef} style={{ height: "20px" }} />
      </div>

      <CModal visible={visible} onClose={() => setVisible(false)} size="lg">
        <CModalHeader onClose={() => setVisible(false)}>
          <CModalTitle>Communication Details</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {selectedCommLog && (
            <>
              <CommLogCard
                commLog={selectedCommLog}
                onUpdate={handleUpdate}
              />
              <CCard className="mb-4">
                <CCardHeader>
                  <strong>Communication Content</strong>
                </CCardHeader>
                <CCardBody>
                  <CRow className="mb-3">
                    <CCol>
                      <strong>Timestamp:</strong> {selectedCommLog.formattedTimestamp}
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol>
                      <strong>Transcript:</strong>
                      <div className="mt-2 p-3 bg-light rounded border text-dark">
                        {selectedCommLog.transcript ? (
                          <TranscriptView
                            transcript={selectedCommLog.transcript}
                            channel={selectedCommLog.channel}
                            summary={selectedCommLog.summary}
                          />
                        ) : (
                          <div style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                            Loading transcript...
                          </div>
                        )}
                      </div>
                    </CCol>
                  </CRow>
                </CCardBody>
              </CCard>
            </>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setVisible(false)}>
            Close
          </CButton>
        </CModalFooter>
      </CModal>
    </>
  );
};

export default LogsTable;