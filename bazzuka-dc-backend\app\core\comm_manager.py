from datetime import datetime, <PERSON><PERSON><PERSON>
import os

from .messenger import <PERSON>
from .issues import IssueRepository
from .defaulters import defaulters
from .lru_cache import LR<PERSON>ache

# from .localstorage import inputs_storage, email_inputs_storage

from .ai import PhonePromptGenerator, make_ai_client
from .ai.payment_likelihood_predictor import predict_payment_likelihood
from .ai.comm_prefs_agent import analyze_communication_preferences
from .ai.payment_agent import analyze_payment_commitments

ai_client = make_ai_client()

from ..utils.supabase.queries import (
    insert_email_comm_combined,
    insert_comm_logs,
    get_emails_by_message_id,
    get_org,
    get_action_items_by_defaulter_id,
    get_defaulter_by_id,
    insert_call_comm,
    get_comm_logs_by_defaulter_id,
    get_org_by_id,
    get_issues_by_defaulter_id,
    get_calllog_by_id,
    get_restrictions_by_defaulter_id,
)

from .vapi import generate_call_forwarding_payload
import concurrent.futures
import re
import holidays


def strip_code_fences(text):
    return re.sub(r"^```html\n|```$", "", text.strip(), flags=re.MULTILINE)


class CachedMessageLookup:
    def __init__(self, cache_size=32):
        self.cache = LRUCache(max_size=cache_size)

    def get(self, message_id):
        """Run a query with LRU caching."""
        # Check the cache first
        cached_result = self.cache.get(message_id)

        if cached_result is not None:
            return cached_result

        # Query the database if not cached
        message = get_emails_by_message_id(message_id).data
        if not message or len(message) == 0:
            # print("Message does not exist.")
            return None
        else:
            # Cache the result
            self.cache.set(message_id, message[0])
            return message

    def put(self, message_id, message):
        """Add a message to the cache."""
        self.cache.set(message_id, message)


def fdcpa_last_call_answered(defaulter_id):
    comms = get_comm_logs_by_defaulter_id(defaulter_id).data
    answered_calls = []
    for c in comms:
        if c.get("channel") == "call":
            calllog_result = get_calllog_by_id(c["comm_id"])
            calllog = getattr(calllog_result, "data", None)
            ended_reason = calllog.get("ended_reason") if calllog else None
            # Treat as answered if ended_reason is NOT in the excluded list
            if ended_reason not in (
                "customer-busy",
                "voicemail",
                "customer-did-not-answer",
            ):
                answered_calls.append(datetime.fromisoformat(c["timestamp"]))
    if not answered_calls:
        return None
    return max(answered_calls)


def fdcpa_attempted_calls_past_seven_days(defaulter_id):
    comms = get_comm_logs_by_defaulter_id(defaulter_id).data
    now = datetime.now()
    seven_days_ago = now - timedelta(days=7)
    # Only consider call logs
    attempted_calls = [
        c
        for c in comms
        if c.get("channel") == "call"
        and datetime.fromisoformat(c["timestamp"]) >= seven_days_ago
        and c.get("direction") == "outbound"
    ]
    return len(attempted_calls)


def is_fdcpa_compliant_org(org_id):
    # Treat every organization as finance by default
    return True


def get_org_id_for_defaulter(defaulter_id):
    defaulter = get_defaulter_by_id(defaulter_id).data[0]
    if "org_id" in defaulter:
        return defaulter["org_id"]
    # fallback: get from issues
    issues = get_issues_by_defaulter_id(defaulter_id).data
    if issues and "org_id" in issues[0]:
        return issues[0]["org_id"]
    return None


def validate_action_items(defaulter_id):
    ac_items = get_action_items_by_defaulter_id(defaulter_id).data
    blocked_channels = (
        get_defaulter_by_id(defaulter_id)
        .data[0]
        .get("customizations", {})
        .get("blocked_channels", [])
    )

    new_blocked_channels = []
    for channel in blocked_channels:
        new_blocked_channels.append(channel[:-1])
    blocked_channels = new_blocked_channels
    # print("TEST BLOCKED CHANNELS: " + str(blocked_channels))

    if len(ac_items) == 0:
        return (
            False,
            "No action items scheduled for this defaulter, there should always be at least one action item.",
        )
    for item in ac_items:
        if item.get("action_channel") in blocked_channels:
            return (
                False,
                f"Action item scheduled for {item.get('action_channel')} is blocked for this defaulter, there should not be any action items scheduled for this channel.",
            )
    outreach_items = [item for item in ac_items if item.get("category") == "outreach"]
    num_outreach_items = len(outreach_items)
    if num_outreach_items == 0:
        return (
            False,
            "No outreach is scheduled for this defaulter. There should be at least one 'outreach' action if there's been no contact or promise to pay, or to follow up in case they drop off after a promise. Create an action item.",
        )

    # --- FDCPA Validation ---
    org_id = get_org_id_for_defaulter(defaulter_id)
    if org_id and is_fdcpa_compliant_org(org_id):
        # Gather all scheduled call action items
        scheduled_calls = [
            item for item in outreach_items if item.get("action_channel") == "call"
        ]
        # Get the date of the last answered call
        last_answered = fdcpa_last_call_answered(defaulter_id)
        if last_answered:
            for item in scheduled_calls:
                scheduled_date = (
                    item.get("action_date") or item.get("date") or item.get("timestamp")
                )
                if scheduled_date:
                    # Convert to datetime if needed
                    if isinstance(scheduled_date, str):
                        try:
                            scheduled_date = datetime.fromisoformat(scheduled_date)
                        except Exception:
                            continue
                    if scheduled_date < last_answered + timedelta(days=7):
                        return (
                            False,
                            f"FDCPA Validation Error: Cannot call within 7 days of last answered call (last answered: {last_answered})",
                        )
        # Gather all past call logs (outbound only)
        comms = get_comm_logs_by_defaulter_id(defaulter_id).data
        call_history = [
            {
                "timestamp": c["timestamp"],
                "direction": c.get("direction"),
                "channel": c.get("channel"),
            }
            for c in comms
            if c.get("channel") == "call" and c.get("direction") == "outbound"
        ]
        # Combine past and scheduled calls
        all_calls = []
        for c in call_history:
            try:
                call_time = c["timestamp"]
                if isinstance(call_time, str):
                    call_time = datetime.fromisoformat(call_time)
                all_calls.append(call_time)
            except Exception:
                continue
        for item in scheduled_calls:
            scheduled_date = item.get("action_date")
            scheduled_time = item.get("action_time")

            if scheduled_date and scheduled_time:
                try:
                    scheduled_datetime = datetime.fromisoformat(
                        f"{scheduled_date} {scheduled_time}"
                    )
                    all_calls.append(scheduled_datetime)
                except Exception:
                    continue

        # print(all_calls)

        all_calls = sorted(all_calls)
        # Rolling 7-day window check
        for i in range(len(all_calls)):
            window_start = all_calls[i]
            window_end = window_start + timedelta(days=7)
            count_in_window = sum(
                1 for call_time in all_calls if window_start <= call_time < window_end
            )
            if count_in_window > 7:
                return (
                    False,
                    "FDCPA Validation Error: More than 7 calls in a rolling 7-day period",
                )
    # If we've made it here, all validations passed
    return (True, "All validations passed")


def is_holiday(date):
    """
    Check if the given date is a federally-recognized US holiday.
    :param date: datetime.date or datetime.datetime object
    :return: True if the date is a US federal holiday, False otherwise
    """
    us_holidays = holidays.country_holidays('US')
    return date in us_holidays


# Helper to get next 60 days of US holidays

def get_next_60_days_us_holidays():
    today = datetime.now().date()
    us_holidays = holidays.country_holidays('US')
    holidays_list = []
    for i in range(60):
        d = today + timedelta(days=i)
        if d in us_holidays:
            holidays_list.append(f"{d}: {us_holidays.get(d)}")
    return holidays_list


class CommManager:
    """
    The Manager class is responsible for handling inbound communications, routing information to the appropriate
    communications interfaces, pre-and-post-communications-processing, and sending outbound communications.

    The Manager is the primary interface for the application.
    """

    def __init__(self):
        self.messenger = Messenger()
        self.issues = IssueRepository()
        self.message_lookup = CachedMessageLookup()
        self.phone_prompt_generator = PhonePromptGenerator()

    def _summarize(self, defaulter_id, summaries, channel, timestamp, direction, text):
        """
        Update the summary for the issue.
        """
        case_info = defaulters.get_defaulter_by_id(defaulter_id)
        if case_info is None:
            raise ValueError(f"Defaulter with ID {defaulter_id} not found")

        # Ensure text is not None
        if text is None:
            text = ""
            
        # Ensure summaries is not None
        if summaries is None:
            summaries = []

        ai_client = make_ai_client()

        new_summary = (
            ai_client.do("summarize")
            .with_context(
                {
                    "case_info": str(case_info),
                    "previous_summaries": str(summaries),
                    "channel": channel,
                    "timestamp": timestamp,
                    "direction": direction,
                    "transcript": text,
                }
            )
            .execute()
        )

        # insert_summary(defaulter_id, new_summary, comm_id)

        return new_summary

    def _invoke_outbound_call(self, data):
        comm_history = self.issues.get_comm_history_mixed(data["defaulter_id"])
        defaulter = defaulters.get_defaulter_by_id(data["defaulter_id"])
        if defaulter is None:
            raise ValueError(f"Defaulter with ID {data['defaulter_id']} not found")

        data["case_info"] = defaulter
        data["strategy"] = defaulter.get("customizations").get("negotiation_strategy")

        if data.get("reason"):
            data["purpose"] = data["reason"]

        return self.messenger.send("call", data)


    def _invoke_outbound_email(self, data):
        # Fetch the summary from the table.
        defaulter_id = data["defaulter_id"]
        comm_history = self.issues.get_comm_history_mixed(defaulter_id)
        defaulter = defaulters.get_defaulter_by_id(defaulter_id)
        if defaulter is None:
            raise ValueError(f"Defaulter with ID {defaulter_id} not found")

        if not data.get("recipientAddress"):
            data["recipientAddress"] = defaulter["email"]
            data["recipientName"] = defaulter["name"]

        # negotiation_strategy = defaulter.get("customizations").get("negotiation_strategy")

        # Compose an email.
        email_subject = (
            "Important Information"  # TODO: Use AI to generate the email subject.
        )

        # args = {
        #     "case_info": {k: v for k, v in defaulter.items() if k != "customizations"},
        #     "strategy": negotiation_strategy,
        #     "commlogs": comm_history
        # }

        # if data.get("reason"):
        #     args["reason"] = data["reason"]

        org_settings = get_org().data[0]["metadata"]
        # verification_types = org_settings.get("verification_types", [])
        # email_inputs_storage.set(defaulter_id, args)
        # for verification_type in verification_types:
        #     field = verification_type["field"]
        #     email_inputs_storage.add_password(
        #         defaulter_id, field, defaulter["customizations"][field]
        #     )

        ai_client = make_ai_client()
        email_text = strip_code_fences(
            ai_client.do("compose_email")
            .with_context(
                {
                    "defaulter_id": defaulter_id,
                    "defaulter_name": defaulter["name"],
                    "org_name": get_org().data[0]["name"],
                    "verification_types": org_settings.get("verification_types", []),
                    # "commlogs": comm_history,
                    "org_type": org_settings.get("type"),
                }
            )
            .execute()
        )

        data["text"] = email_text
        data["subject"] = email_subject

        # Send the message
        message_id = self.messenger.send("email", data)

        # Update the summary.
        new_summary = self._summarize(
            defaulter_id,
            comm_history,
            "email",
            data["timestamp"],
            "outbound",
            email_text,
        )

        # Update the CommLog, and EmailLog tables.
        insert_email_comm_combined(
            data["timestamp"],
            message_id,
            defaulter_id,
            email_subject,
            email_text,
            "outbound",
            new_summary,
        )

        org_metadata = get_org().data[0]["metadata"]
        org_collections_strategy = org_metadata.get("strategy")

        restrictions_result = get_restrictions_by_defaulter_id(defaulter_id)
        holidays_list = []
        if restrictions_result.data:
            restrictions = restrictions_result.data[0].get("description")
            holidays_list = get_next_60_days_us_holidays()
        else:
            restrictions = "There are currently no time, holiday, or day of week restrictions for this user."

        ai_args = {
            "current_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "defaulter_id": defaulter_id,
            "case_info": defaulter,
            "current_conversation_summary": new_summary,
            "current_conversation_timestamp": data["timestamp"],
            "current_conversation_direction": "inbound",
            "previous_conversation_logs": comm_history,
            "collections_strategy": org_collections_strategy,
            "negotiation_strategy": defaulter["customizations"].get(
                "negotiation_strategy"
            ),
            "restrictions": restrictions,
        }
        if holidays_list:
            ai_args["holidays"] = ", ".join(holidays_list)
        else:
            ai_args["holidays"] = ""

        max_retries = 3
        for i in range(max_retries):
            ai_client.do("analyze").with_context(ai_args).execute()
            is_valid, error = validate_action_items(defaulter_id)
            if is_valid:
                break
            ai_args["error"] = error

        ac_items = get_action_items_by_defaulter_id(defaulter_id)

        # Get most recent action item
        if ac_items.data:
            most_recent_action_item = sorted(
                ac_items.data, key=lambda x: x["action_date"]
            )[0]
            # print("Most recent action item:")
            # print(most_recent_action_item)

    def _handle_end_of_call(self, data):
        # Note: this function is to be called after a call has ended.
        # Since VAPI will store our calls, we just need to add to the CommLog.
        # Update the summary, determine next action.
        # defaulter_id: int, channel: str, comm_id: int, direction: str

        # if defaulter_id is not found, look it up in defaulters
        defaulter = None
        if "defaulter_id" not in data:
            defaulter = defaulters.get_defaulter_by_phone(data["caller_number"])
        else:
            defaulter = defaulters.get_defaulter_by_id(data["defaulter_id"])

        if defaulter is None:
            raise ValueError(f"Defaulter not found for call data: {data}")

        data["defaulter_id"] = defaulter["id"]

        print(
            f"[CommManager] ended_reason received in data: {data.get('ended_reason')}"
        )

        summaries = self.issues.get_comm_history(data["defaulter_id"])

        new_summary = self._summarize(
            data["defaulter_id"],
            summaries,
            "call",
            data["timestamp"],
            data["direction"],
            data["text"],
        )

        if data.get("ended_reason") == "voicemail":
            payment_likelihood = 0
        else:
            payment_likelihood = predict_payment_likelihood(
                data["text"], "call", summaries
            )

        analyze_communication_preferences(data["text"], data["defaulter_id"])
        analyze_payment_commitments(data["text"], data["defaulter_id"])

        insert_comm_logs(
            data["defaulter_id"],
            "call",
            data["call_id"],
            data["direction"],
            data["timestamp"],
            new_summary,
            payment_likelihood=payment_likelihood,
        ).data[0]

        print(
            f"[CommManager] Passing ended_reason to insert_call_comm: {data.get('ended_reason')}"
        )
        insert_call_comm(
            data["call_id"],
            data["recording_url"],
            data["call_duration"],
            data.get("ended_reason"),
        )

        comm_logs = self.issues.get_comm_history_mixed(data["defaulter_id"])
        org_metadata = get_org().data[0]["metadata"]
        org_collections_strategy = org_metadata.get("strategy")

        # # Set up verification types and protected storage
        # verification_types = org_metadata.get("verification_types", [])
        # inputs_storage.set(data["defaulter_id"], data)
        # for verification_type in verification_types:
        #     field = verification_type["field"]
        #     inputs_storage.add_password(
        #         data["defaulter_id"], field, defaulter["customizations"][field]
        #     )

        restrictions_result = get_restrictions_by_defaulter_id(data["defaulter_id"])
        holidays_list = []
        if restrictions_result.data:
            restrictions = restrictions_result.data[0].get("description")
            holidays_list = get_next_60_days_us_holidays()
        else:
            restrictions = "There are currently no time, holiday, or day of week restrictions for this user."

        ai_args = {
            "current_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "defaulter_id": data["defaulter_id"],
            "case_info": defaulter,
            "current_conversation_summary": new_summary,
            "current_conversation_timestamp": data["timestamp"],
            "current_conversation_direction": data["direction"],
            "previous_conversation_logs": comm_logs,
            "collections_strategy": org_collections_strategy,
            "negotiation_strategy": defaulter["customizations"].get(
                "negotiation_strategy"
            ),
            "restrictions": restrictions,
        }
        if holidays_list:
            ai_args["holidays"] = ", ".join(holidays_list)
        else:
            ai_args["holidays"] = ""

        max_retries = 3
        for i in range(max_retries):
            print(f"[CommManager] Running AI analysis (attempt {i+1}/{max_retries}) for defaulter {data['defaulter_id']}")
            print(f"[CommManager] AI ARGS SENT TO ANALYZER: {ai_args}")
            ai_client.do("analyze").with_context(ai_args).execute()
            is_valid, error = validate_action_items(data["defaulter_id"])
            if is_valid:
                print(f"[CommManager] AI analysis completed successfully for defaulter {data['defaulter_id']}")
                break
            print(f"[CommManager] AI analysis validation failed, retrying with error: {error}")
            ai_args["error"] = error

    def _handle_inbound_email(self, data):
        """
        The data should be a dictionary with the following keys
        - text: str
        - subject: str
        - senderAddress: str
        - recipientAddress: str
        - timestamp: str
        """
        defaulter = defaulters.get_defaulter_by_email(data["senderAddress"])
        if not defaulter:
            # print("No defaulter associated with sender address.")
            return

        defaulter_id = defaulter["id"]
        if not defaulter_id:
            # print("No issue associated with sender address.")
            # TODO: Maybe respond asking for more details.
            return

        if self.message_lookup.get(data["id"]):
            # print("Message already exists. Returning early.")
            return

        comm_history = self.issues.get_comm_history_mixed(defaulter_id)
        new_summary = self._summarize(
            defaulter_id,
            comm_history,
            "email",
            data["timestamp"],
            "inbound",
            data["text"],
        )

        payment_likelihood = predict_payment_likelihood(
            data["text"], "email", comm_history
        )

        analyze_communication_preferences(data["text"], defaulter_id)
        analyze_payment_commitments(data["text"], defaulter_id)

        insert_message_result = insert_email_comm_combined(
            data["timestamp"],
            data["id"],
            defaulter_id,
            data["subject"],
            data["text"],
            "inbound",
            new_summary,
            payment_likelihood=payment_likelihood,
        )

        if insert_message_result:
            self.message_lookup.put(
                data["id"], insert_message_result["emaillogs"]["data"][0]
            )

        email = f"From: <{data['senderAddress']}>\nSubject: {data['subject']}\nBody: {data['text']}"

        # eml_args = {
        #     "case_info": {k: v for k, v in defaulter.items() if k != "customizations"},
        #     "inbound_msg": email,
        #     "strategy": defaulter["customizations"].get("negotiation_strategy"),
        #     "commlogs": comm_history
        # }

        # Set up verification types and protected storage
        org_metadata = get_org().data[0]["metadata"]
        verification_types = org_metadata.get("verification_types", [])
        # email_inputs_storage.set(defaulter_id, eml_args)
        # for verification_type in verification_types:
        #     field = verification_type["field"]
        #     email_inputs_storage.add_password(
        #         defaulter_id, field, defaulter["customizations"][field]
        #     )

        ai_client = make_ai_client()
        org = get_org().data[0]
        outbound_msg = strip_code_fences(
            ai_client.do("compose_email")
            .with_context(
                {
                    "defaulter_id": defaulter_id,
                    "defaulter_name": defaulter["name"],
                    "org_name": org["name"],
                    "org_type": org["metadata"].get("type"),
                    "verification_types": verification_types,
                    "inbound_message": email,
                }
            )
            .execute()
        )

        outbound_data = {}
        outbound_data["recipientAddress"] = data["senderAddress"]
        outbound_data["text"] = outbound_msg
        outbound_data["subject"] = data["subject"]
        outbound_data["reply_to_message_id"] = data["id"]

        message_id = self.messenger.send("email", outbound_data)

        new_summary = self._summarize(
            defaulter_id,
            comm_history,
            "email",
            data["timestamp"],
            "outbound",
            outbound_msg,
        )

        insert_email_comm_combined(
            "(now())",
            message_id,
            defaulter_id,
            outbound_data["subject"],
            outbound_data["text"],
            "outbound",
            new_summary,
        )

        org_metadata = get_org().data[0]["metadata"]
        org_collections_strategy = org_metadata.get("strategy")

        restrictions_result = get_restrictions_by_defaulter_id(defaulter_id)
        holidays_list = []
        if restrictions_result.data:
            restrictions = restrictions_result.data[0].get("description")
            holidays_list = get_next_60_days_us_holidays()
        else:
            restrictions = "There are currently no time, holiday, or day of week restrictions for this user."

        ai_args = {
            "current_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "defaulter_id": str(defaulter_id),
            "case_info": str(defaulter),
            "current_conversation_summary": new_summary,
            "current_conversation_timestamp": data["timestamp"],
            "current_conversation_direction": "inbound",
            "previous_conversation_logs": comm_history,
            "collections_strategy": org_collections_strategy,
            "negotiation_strategy": defaulter["customizations"].get(
                "negotiation_strategy"
            ),
            "restrictions": restrictions,
        }
        if holidays_list:
            ai_args["holidays"] = ", ".join(holidays_list)
        else:
            ai_args["holidays"] = ""

        max_retries = 3
        for i in range(max_retries):
            print(f"[CommManager] Running AI analysis for inbound email (attempt {i+1}/{max_retries}) for defaulter {defaulter_id}")
            print(f"[CommManager] AI ARGS SENT TO ANALYZER: {ai_args}")
            ai_client.do("analyze").with_context(ai_args).execute()
            is_valid, error = validate_action_items(defaulter_id)
            if is_valid:
                print(f"[CommManager] AI analysis completed successfully for inbound email defaulter {defaulter_id}")
                break
            print(f"[CommManager] AI analysis validation failed for inbound email, retrying with error: {error}")
            ai_args["error"] = error

        ac_items = get_action_items_by_defaulter_id(defaulter_id)

        # Get most recent action item
        if ac_items.data:
            most_recent_action_item = sorted(
                ac_items.data, key=lambda x: x["action_date"]
            )[0]
            # print("Most recent action item:")
            # print(most_recent_action_item)
            # Update most recent commlog payment likelihood
            # if most_recent_action_item["category"] == "payment_reminder":
            #    update_most_recent_commlog_payment_likelihood(defaulter_id, most_recent_action_item["payment_likelihood"])

    def send(self, channel, data):
        """
        For now, this is used to invoke one-off (unscheduled) outbound communications.
        Whether we will need this feature can be discussed later. Useful for testing.
        """
        if not data.get("timestamp"):
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S%z")
        if channel == "call":
            self._invoke_outbound_call(data)
        elif channel == "email":
            self._invoke_outbound_email(data)
        else:
            raise ValueError("Invalid channel.")

    def process(self, channel, data):
        """
        Note this will not be used to process inbound messages only.
        We are transitioning so that outbound messages can be processed as well.
        """
        if channel == "call":
            self._handle_end_of_call(data)
        elif channel == "email":
            self._handle_inbound_email(data)
        else:
            raise ValueError("Invalid channel.")

    def execute(
        self,
        defaulter_id,
        action_channel,
        action_channel_content=None,
        action_channel_reason=None,
    ):
        """
        Execute an action item.
        """
        # print(f"[DEBUG] comm_manager.execute called with defaulter_id={defaulter_id}, action_channel={action_channel}, action_channel_content={action_channel_content}, action_channel_reason={action_channel_reason}")
        issue = defaulters.get_defaulter_by_id(defaulter_id)
        if issue is None:
            # print(f"[ERROR] No defaulter found for defaulter_id={defaulter_id}")
            return
            
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S%z")
        if action_channel == "call":
            recipientNumber = issue["phone"]
            recipientName = issue["name"]
            # print(f"[DEBUG] Outbound call: recipientNumber={recipientNumber}, recipientName={recipientName}")
            return self._invoke_outbound_call(
                {
                    "reason": action_channel_reason,
                    "recipientNumber": recipientNumber,
                    "recipientName": recipientName,
                    "defaulter_id": defaulter_id,
                    "timestamp": timestamp,
                }
            )
        elif action_channel == "email":
            email_address = issue["email"]
            text = action_channel_content
            # print(f"[DEBUG] Outbound email: email_address={email_address}, text={text}")
            return self._invoke_outbound_email(
                {
                    "reason": action_channel_reason,
                    "email": email_address,
                    "text": text,
                    "defaulter_id": defaulter_id,
                    "timestamp": timestamp,
                }
            )
        else:
            # print(f"[ERROR] Unsupported channel: {action_channel}")
            raise ValueError("Unsupported channel.")

    # def make_call_prompt_args(self, phone_number):
    #     """
    #     Get the call prompt arguments.
    #     """
    #     defaulter = defaulters.get_defaulter_by_phone(phone_number)
    #     return {
    #         "defaulter_id": defaulter["id"],
    #         "case_info": defaulter,
    #         "strategy": self.issues.get_strategy(defaulter["id"]),
    #     }

    def get_assistant(self, caller_number):
        """
        We really need a better place for this!!!!!
        """
        case_info = defaulters.get_defaulter_by_phone(caller_number)
        if case_info is None:
            raise ValueError(f"Defaulter with phone number {caller_number} not found")
            
        defaulter_id = case_info["id"]

        # case = defaulters.get_defaulter_by_id(defaulter_id)
        caller_name = case_info["name"]
        org = get_org().data[0]
        org_metadata = org["metadata"]

        ai_args = {}
        # ai_args["case_info"] = {k: v for k, v in case_info.items() if k != "customizations"}
        ai_args["defaulter_id"] = defaulter_id
        ai_args["defaulter_name"] = caller_name
        # ai_args["strategy"] = case["customizations"].get("negotiation_strategy")
        if org_metadata.get("call_forwarding"):
            ai_args["call_forwarding"] = org_metadata["call_forwarding"]
        if org_metadata.get("verification_types"):
            ai_args["verification_types"] = org_metadata["verification_types"]
        ai_args["direction"] = "inbound"
        # ai_args["commlogs"] = self.issues.get_comm_history(defaulter_id)
        ai_args["org_type"] = org_metadata.get("type")
        ai_args["org_name"] = org.get("name")
        ai_args["promise_failure_policy"] = org_metadata.get("promise_failure_policy")
        ai_args["policy"] = org_metadata.get("settlement_plans")

        messages = self.phone_prompt_generator.generate(ai_args)

        # Set up verification types and protected storage
        # verification_types = org_metadata.get("verification_types", [])
        # inputs_storage.set(defaulter_id, ai_args)
        # for verification_type in verification_types:
        #     field = verification_type["field"]
        #     inputs_storage.add_password(
        #         defaulter_id, field, case_info["customizations"][field]
        #     )

        assistant_response = {
            "name": "Debby",
            "firstMessage": f"Hey {caller_name}, this is Debby, please note this is a recorded line. How can I help you today?",
            "model": {
                "provider": "openai",
                "model": "ft:gpt-4.1-2025-04-14:bazzuka-ai:jul-07-sft-dpo:BqZzjdGF",
                "messages": messages,
                "tools": [
                    {
                        "type": "function",
                        "async": False,
                        "function": {
                            "name": "get_info",
                            "description": "Get the information about the defaulter and case.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "defaulter_id": {
                                        "type": "string",
                                        "description": "The defaulter_id from the system prompt.",
                                    },
                                    "verification_type": {
                                        "type": "string",
                                        "description": "The type of verification for organizations requiring verification. The only supported values are 'ssn' meaning last four digits of Social Security Number, and 'dob' meaning date of birth.",
                                    },
                                    "verification_value": {
                                        "type": "string",
                                        "description": "The verification value for organizations requiring verification. For 'ssn', this should be the defaulter's Social Security Number, for 'dob', it should be the user's date of birth parsed as 'MM/DD/YYYY'.",
                                    },
                                },
                                "required": [
                                    "defaulter_id",
                                ],
                            },
                        },
                    },
                    {
                        "type": "function",
                        "async": True,
                        "function": {
                            "name": "schedule_one_off_communication",
                            "description": "Schedule a one-off communication with the user. This should be used if the user requests a one-off communication, or if the user is not interested in a payment plan.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "defaulter_id": {
                                        "type": "number",
                                        "description": "The user's defaulter_id.",
                                    },
                                    "channel": {
                                        "type": "string",
                                        "description": "The channel to communicate with the user. This can be 'email' or 'call'.",
                                    },
                                    "date": {
                                        "type": "string",
                                        "description": "The date of the communication in the format 'YYYY-MM-DD'.",
                                    },
                                    "time": {
                                        "type": "string",
                                        "description": "The time of the communication in the format 'HH:MM'.",
                                    },
                                    "reason": {
                                        "type": "string",
                                        "description": "The reason for the communication.",
                                    },
                                    "is_human_followup": {
                                        "type": "boolean",
                                        "description": "Whether the follow-up should be with a human (true) or AI (false). Defaults to false (AI)."
                                    },
                                },
                                "required": [
                                    "defaulter_id",
                                    "channel",
                                    "date",
                                    "time",
                                    "reason",
                                    "is_human_followup"
                                ],
                            },
                        },
                    },
                ],
            },
            "voice": {
                "provider": "11labs",
                "voiceId": "paula",
            },
            "serverUrl": os.getenv("ROOT_DOMAIN_PUBLIC") + "/v0/webhook/vapi",
        }

        if org_metadata.get("call_forwarding"):
            assistant_response["model"]["tools"].append(
                generate_call_forwarding_payload(org_metadata["call_forwarding"])
            )

        return assistant_response


comm_manager = CommManager()
