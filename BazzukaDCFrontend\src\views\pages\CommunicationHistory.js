import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { CCard, CCardBody, CCardHeader } from '@coreui/react';
import { cilChatBubble } from '@coreui/icons';
import CIcon from '@coreui/icons-react';
import { useFormattedTimestamp } from '../../utils/dateUtils';
import { formatTranscript } from '../../utils/textUtils';
import { getApiUrl } from '../../utils/apiConfig'

const CommunicationHistory = () => {
  const { defaulterId } = useParams();
  const [communications, setCommunications] = useState([]);
  const [loading, setLoading] = useState(true);
  const formatDate = useFormattedTimestamp();

  useEffect(() => {
    const fetchCommunications = async () => {
      try {
        const response = await fetch(getApiUrl(`/v0/communications/user/${defaulterId}`));
        const data = await response.json();
        setCommunications(data.data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching communications:', error);
        setLoading(false);
      }
    };

    fetchCommunications();
  }, [defaulterId]);

  const getDirectionIcon = (direction) => {
    return direction === 'outbound' ? '↗️' : '↙️';
  };

  const getChannelIcon = (channel) => {
    return channel === 'call' ? '📞' : '📧';
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container-fluid">
      <h2 className="mb-4">Communication History</h2>
      <div className="row">
        <div className="col-12">
          {communications.map((comm, index) => (
            <CCard key={comm.id || comm.comm_id} className="mb-3">
              <CCardHeader className="d-flex justify-content-between align-items-center">
                <div>
                  <span className="me-2">{getDirectionIcon(comm.direction)}</span>
                  <span className="me-2">{getChannelIcon(comm.channel || 'email')}</span>
                  <strong>{comm.email_subject || 'Call'}</strong>
                </div>
                <small className="text-muted">{formatDate(comm.timestamp)}</small>
              </CCardHeader>
              <CCardBody style={{ padding: '8px 12px' }}>
                {comm.channel === 'call' ? (
                  <div>
                    <p style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>
                      <strong>Summary:</strong> {comm.summary}
                    </p>
                    <div className="bg-light rounded" style={{ padding: '8px' }}>
                      <div
                        className="transcript-content text-dark"
                        style={{ lineHeight: '1.5', margin: 0, padding: 0 }}
                        dangerouslySetInnerHTML={{ __html: formatTranscript(comm.transcript, comm.channel) }}
                      />
                    </div>
                  </div>
                ) : (
                  <div
                    className="transcript-content text-dark bg-light rounded"
                    style={{ margin: 0, padding: '8px', lineHeight: '1.5' }}
                    dangerouslySetInnerHTML={{ __html: formatTranscript(comm.transcript, comm.channel) }}
                  />
                )}
              </CCardBody>
            </CCard>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CommunicationHistory;