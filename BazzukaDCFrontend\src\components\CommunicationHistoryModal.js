import React, { useState, useEffect } from 'react';
import {
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
} from '@coreui/react';
import { useFormattedTimestamp } from '../utils/dateUtils';
import TranscriptView from './TranscriptView';
import { getApiUrl } from '../utils/apiConfig'

const CommunicationHistoryModal = ({ show, onClose, defaulterId }) => {
  const [communications, setCommunications] = useState([]);
  const [loading, setLoading] = useState(true);
  const formatDate = useFormattedTimestamp();

  useEffect(() => {
    const fetchCommunications = async () => {
      if (!defaulterId) return;

      try {
        const response = await fetch(getApiUrl(`/v0/communications/user/${defaulterId}`));
        const data = await response.json();
        setCommunications(data.data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching communications:', error);
        setLoading(false);
      }
    };

    if (show) {
      fetchCommunications();
    }
  }, [defaulterId, show]);

  const getDirectionIcon = (direction) => {
    return direction === 'outbound' ? '↗️' : '↙️';
  };

  const getChannelIcon = (channel) => {
    return channel === 'call' ? '📞' : '📧';
  };

  return (
    <CModal visible={show} onClose={onClose} size="xl" scrollable>
      <CModalHeader>
        <CModalTitle>Communication History</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {loading ? (
          <div>Loading...</div>
        ) : (
          <div className="container-fluid">
            {communications.map((comm, index) => (
              <CCard key={comm.id || comm.comm_id} className="mb-3">
                <CCardHeader className="d-flex justify-content-between align-items-center">
                  <div>
                    <span className="me-2">{getDirectionIcon(comm.direction)}</span>
                    <span className="me-2">{getChannelIcon(comm.channel || 'email')}</span>
                    <strong>{comm.email_subject || 'Call'}</strong>
                  </div>
                  <small className="text-muted">{formatDate(comm.timestamp)}</small>
                </CCardHeader>
                <CCardBody>
                  <TranscriptView 
                    transcript={comm.transcript}
                    channel={comm.channel}
                    summary={comm.summary}
                  />

                </CCardBody>
              </CCard>
            ))}
          </div>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Close
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CommunicationHistoryModal;